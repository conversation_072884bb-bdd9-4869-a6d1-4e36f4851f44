import base64

# Base64 (apenas exemplo, encurte ou pegue do início ao fim corretamente)
base64_string = """MIIOsAIBAzCCDmwGCSqGSIb3DQEHAaCCDl0Egg5ZMIIOVTCCBcYGCSqGSIb3 DQEHAaCCBbcEggWzMIIFrzCCBasGCyqGSIb3DQEMCgECoIIE/jCCBPowHAYK KoZIhvcNAQwBAzAOBAgrUlZPcxrNWAICB9AEggTY+4rwajkIXpOGqBuuYxMc JpS7PkIjFdZzBjYeB6AZjg4ZP7Sq55RF26ZpiNDf82T6HZiQ9qYPxVfZcMpA ldJcZ8ajhFLXY3ONE7rS8YCvnsDEwSHolZ1IdIn889Y7/cv/DHAIkOJmvlEB rsXuCcQurUpfZC/EnHOcTf5Q7kqw3knkZ2UrVUp5ZEPjdcYNGhlcOwrWPqXF Z9YKXUQ50GZVkbMw5VXQtEaTPsRtj1a6xXJAJywPMfGIKP5w1jl3Vd5tIRXc B7DdAMpz4++Z86TKn24/ZHO3WiR7+YrLhav002owr0o3u6d7JTZD9iOshtgj Q7N92SPlILfxzHxINKpw4xJpQoNdVGAr7hVTEmMNzVqfB751CVTTB1b+Gvmi nG2enDs1ImN/ewnvktvE6kczgU2ghF5lC3cB2E2gqq2txiIccU93W/VEyi0F s65Vi0wvpdbkSneD7M+yWg5rn+nKPNmTZZTSAXvpUFYH2eyB4mg5m405uXwm D+23FKGhZHIt1CuaT4tvxR0BqjYvAg5raNJ3PoLlA/C3Bm2zBQnD4E2CNLwb CGwdBrpnbJ2kiIPK2gHUE0qRjaDraJ5GHX40Y4BTao9NFqqLFlxYqoj9uOz9 wbgth8v6juYTKCpIqJeavLNdjaeQMJMjUWkh0BGKtTKvXXgQyd41YaUYSBYN phBRfRak1znUMWB2aKOxBZlib/2KN1fgODdB72Szig/BVOpBjbsdB0SHQsqN 9PU23HQ9ha6hBrYO/xZYQQLowApkyTGVAyXhEMsyQzQQtRYnx8HyphgqinMQ olwfkF7oTkuy3eKvYYy5T+tnKU/BpsLdUf4zcEjbEDy1PNFxpmnLvR3PtGUb nHrCCjx2j1Z9k4tMDaqbldJz8kNSizZ7/8BWOsnijS6lYFBYkH0oUWLQ25O6 68smqhm/t7fbV2cEzWQjhVHMR+6RJ36JaOJNbLcGEfWZEwGzR/aBX6fi3gbI dlKBlU3KT+O9I2+6aVKWtUWfr+qWet2HWldrqZdNyvvENV//NiksjltYTUXd ab/oj1A9wk+BHCABLCMTOxHuil1DbXWn/HnrEjXy5pQPBCIG/NmH790B/AxK JWBXrA2Nx3ZON8UG333yoyPEDS96WlEQ/jnoqCkDOY0bqmFj/GbEt/e6za2I 4Zdp9f2pNgPLO0A9CUq3vEgaSfTSNWWKz0wKw/CRdaz47Jf0cFNAnojujDun rYa7STluDkS9vFPXYJuwxSLEql7a+lHA3W0wvst4x6IWPqQgPylXUaLRXCxJ avHBsLY1GJWdK0b25pH0tOqg8ZIwD0/kMPJtiWlXGd9OLMx8DmvEOoydRqwE U/p0/PeskxRkdYiAC6O7RZFwe+ZJLlJncMYmTqa51YqU1+LFHKmNiGi4SamJ Pyn1V57Sji4TS/5ZX/D0O9Y+ifzqbeOISQrb0lUATKy/OdDIITt7lOokAdGA jxTw7HOfI+S8qCEokwvgxFr0iPhdVsBmT4f2wpkc6++C+LhSvDEB3SDEMwwg ST08J01MUIu1GD+tGvPKgQ2KP5YhI5XT1vWb2xLhweWAFH52iOtxBJmG14MK OYG9eaH5p+l4r/Zxd7qeM/RQtOwNFNhN6EgvFaHqbYM/ZKfQzDHhlzDxfyjr PPzGznAkSxW9QTGBmTATBgkqhkiG9w0BCRUxBgQEAQAAADAjBgkqhkiG9w0B CRQxFh4UADEAMAAwADYAOQA4ADAAOQA3ADMwXQYJKwYBBAGCNxEBMVAeTgBN AGkAYwByAG8AcwBvAGYAdAAgAFMAdAByAG8AbgBnACAAQwByAHkAcAB0AG8A ZwByAGEAcABoAGkAYwAgAFAAcgBvAHYAaQBkAGUAcjCCCIcGCSqGSIb3DQEH BqCCCHgwggh0AgEAMIIIbQYJKoZIhvcNAQcBMBwGCiqGSIb3DQEMAQMwDgQI wSYzLV7SfkECAgfQgIIIQNICBmgZM3EZsO0NFVA2s1UgSutIXn34F5d3R5Un NuzxSVDEwOTXudd6QeTUCfMu4qPGTxl3kYcLJ3RqZJeCb6DafHUGsPB4ysxk yoUSjjnkVXkrYeU4mavInpLefjtwAQweEgfW0T8lexS3onGSk7HOeCSxirnb //2YrTqDqyLHyvZceXCn6jMyvI3IXe0hx27IiMEC740KUOl6bfLq+IoKDRzs 1M/CghXhoBjuCuKBMfzV8nC3BJ1LHPiaHBNGzfuhb9s1hl+lCMBaAoHFcN6f fy/E9KayWG1quf7yQ8/xjK+LVspWSdDXbXxw0hN7/o3C+IdziLm/BXByPchq qPhJ5gLYiMt2Ahuo/VbaJDoylTq/V5NLDzAvqE7a+EQVgw0w53IURMCKJREy cpISPMOctyuiZ4ntcmZDqSsa7bFqm5+***************************** oPoDLj4KGDC6KCCK6FMg8l+u+FDArzaQIZVbCFo33LhfL/+SkDnleKLLFPE4 am2WpIemf6c3QJYTBn5agGh/ZuB9450ycHn6jGy4yf/Qij901nT1Hv2WIqx5 qi/LKYXhp/2Gw6lZDocTYPrGy0k6QYUT653ahK4B4g2hcVNTgx4x+6hLAZel F84B2zS/Jva4FNfs2h0Zq5Clmbupc2TIZaZ2k8EwoKRFqNZC6AnMRFhWSnCA 2oOLWhZbDa9/vRpgklOrJxM5oSCe2BWPAaS963PGEL9kXb5s0Cwh4upNllE3 UKN6nhozqr5GwyO9xwKwKze2uQck7o1VDbMOymnYpVeMs3Pr0x3on8NNKq1Y 6f2kLuV5CtCS4i59fIML7rUNFAvS5yzZB9DSxsYHxz5Pu06e67da47hCiTDi 6Pty7M8RdwRbyzlETHDmGDdO52HXWRvizIkb7dc/jAAEkpVjehf5iL5T+SEg viyuGjJZx3irBhzhiKR/osb66jc36zudGZt7du56djTONJ/ZAkC4Pmk/rhQ4 J1iTV6WfYXX1sTnlcMxpopLoTr8IWMJ6BFzP32D1qy10gv6/6jcujNP+ij8R fYOJY3fJh1Yf1zeYhk45/oBuiS5GjtCXHeLThRbAM5lqhWceBiiezZja9yI6 QOJ1T8UsSkE+K3j6z7IupWMRO4H/q/+yYGlJIpHN9pYXYrTHo4dLwTD7U0xn fn5DQ6r7WlRYOR28TQ5ochh5ntpLqTV52FjdZJzWWaPQsEpOaGiA2zXrDKI2 qSw267sx+q0rSSYcY7J/DG+Bw2JGeLzKnsGkZDoMRl23kvjzV/S+kU3LuB9w JrjJZu8qidFXSk/Lq0xZUGEjL46HjlmdGn3uJa1/eQc3Dwog5kXmtlCmXXfs S3avNYMJ1gDEORQ2qO+eGbZtK1cEK7o0Siio9fAhFJYm2X+rwoZBJAVJFdh5 xwAaMYkaF84DfFV5S9fuOht0RvN0Ndta8P5Ao8k4+69cXg5jza3niscjngzo uCIQ8/SeivVn8ZlU213/BZUttk/6I7rotrybt08K7mFzy2tEBmxAgVklZ33J 5R0yQFdig5I6Fm6c1cEQUuIbwPM+JzLDrIP1xLTohCye0Q9+Mb7oyM/nru65 LM5UR6nIA10bAVIDvpdm5nPCHhnOsusewx1AhnpwmbMbKEtd/+TWelI++SWF TTdgexJ9TtUz4m21sf7IfM8CHwfRzCxDZyH3cd8udg6IIFYQCaTyTRvp1yZQ g+5p41gbqDtqUP/Mfx4HD1ZVpCWyXHH5fc2yRipKyOM+WbD8mBBL2qIfQS0x a/4ANyfPWIRmYokQqVxSX7nF+zgTY73WrhtKNJIA/2DuF1boLZLrQkeFwQoh btywDEvnpx5bMkQOT2v55CA2oAgYgR5MD6w8rTBpY7sAvyGcVihUyCvi3QsQ XEoShOy8eJztioY7xPOoXsXQuT96476dD8MSBMMCK1tcdz0rHn1VByCbPLHV sPqZjY2oV+FQZ7B4pJFyxlBg3krBrHFGOdvmTuLcLqqAikSBDfG4vprSCi2y eBMFCrnXV5OHPXYoci1C4wraaB4cJLjCVOnaHlVJgqKtnObTR7VRETOonlRM cVR2E1Jpmso+Fd8yKkKBOmQMtf2TDo/MC1W/5/tv90DbYjazCNu+F/JWju9v rvQSWccJyWwbQJ31f5IMkTJOu4DCr8Jjdzwgk39NA5ielo0ojilZ6whABbdH ksrAMsDr8CJV++fxLaHe1Qv4mRToZ7JOn2WpVI2iDqDGX398wkCCeFsBvehb LzxQb3gsPOM2W+0z/jyKSTGUNPf8zahJVEGH6eWBT5+6ROgwpvad1IP9gPZ+ 2gxsDn4JYg3ndNcd4g2KHns/u8l3QiZvtyN7264SN/deyzn8Gbuvw0YsT7PG tAxJ5x+O0vFkuKYbcOdx8xfUEkNUd1zPrPjiKRQIJT6a5Zv3uDnSFSwDLlyD a7XRW0UWkZzqiNAqcfm/Ka+A1lHNWFSlJiSKkwaPZFpo8AMy9vn1WQV63R3f GUSepyMZQTVu1uvkptSmFYa/f7o0lpkAg7I/Qdhk1bMlPETY1fHuXkD1onCx geyGLHjAeB4xQJG0Ym9AoTGZWxMko3qdzqj29ahJ2lAo2xT+uPGoxU67bWsY plq7xvHCHsZHA+Kp++VG2K0q3ajXlRpqqHJWyubLEoiT3el9sNL9yChKJB+B IvhMIeRungvPNeuRNiZwJCZKbD5rcplMpPSTSbTI0eboKgwzed//aEz88xvW SqJ6KW+/qL2SkmuFhrYMbkt6NFs3++V7Y9i/wrJvOg5UDQmn+kp2HoJg1mrb +Pwxw6Qn9o3Fm3K6rTA7MB8wBwYFKw4DAhoEFO4bK1uZjzdHRwTXr4XIpIDS Jet0BBScU1C+/XQdi1HW/lkC3nwoNFyY5wICB9A= """

# Remova espaços, quebras de linha etc.
clean_base64 = base64_string.replace('\n', '').replace('\r', '').replace(' ', '')

# Adiciona padding se necessário
missing_padding = len(clean_base64) % 4
if missing_padding:
    clean_base64 += '=' * (4 - missing_padding)

# Decodifica e escreve o arquivo .pfx
try:
    with open("certificado.pfx", "wb") as f:
        f.write(base64.b64decode(clean_base64))
except Exception as e:
    print(f"Erro ao decodificar: {e}")
    print(f"Tamanho da string base64: {len(clean_base64)}")
    print(f"Primeiros 100 caracteres: {clean_base64[:100]}")
    print(f"Últimos 100 caracteres: {clean_base64[-100:]}")
    exit(1)

print("Arquivo .pfx salvo como certificado.pfx")