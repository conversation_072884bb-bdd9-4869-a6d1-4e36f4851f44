import base64

# Base64 (apenas exemplo, encurte ou pegue do início ao fim corretamente)
base64_string = """MIIjkQIBAzCCI1cGCSqGSIb3DQEHAaCCI0gEgiNEMIIjQDCCHYsGCSqGSIb3 DQEHAaCCHXwEgh14MIIddDCCCKIGCyqGSIb3DQEMCgEDoIIH/jCCB/oGCiqG SIb3DQEJFgGgggfqBIIH5jCCB+IwggXKoAMCAQICCwDI61iWhALS6GEOMA0G CSqGSIb3DQEBCwUAMFsxCzAJBgNVBAYTAkJSMRYwFAYDVQQLDA1BQyBTeW5n dWxhcklEMRMwEQYDVQQKDApJQ1AtQnJhc2lsMR8wHQYDVQQDDBZBQyBTeW5n dWxhcklEIE11bHRpcGxhMB4XDTI1MDYwMzIwMjUyNVoXDTI2MDYwMzIwMjUy NVowgdMxCzAJBgNVBAYTAkJSMRMwEQYDVQQKDApJQ1AtQnJhc2lsMSIwIAYD VQQLDBlDZXJ0aWZpY2FkbyBEaWdpdGFsIFBKIEExMRkwFwYDVQQLDBBWaWRl b2NvbmZlcmVuY2lhMRcwFQYDVQQLDA4zNDk3OTA5ODAwMDE5MjEfMB0GA1UE CwwWQUMgU3luZ3VsYXJJRCBNdWx0aXBsYTE2MDQGA1UEAwwtVFJBTlNQT1JU QURPUkEgVFJBTlNMRU9ORSBMVERBOjgzNzM5OTQ2MDAwMTAwMIIBIjANBgkq hkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA/Gnie5rL76VkQIkRzyuFIRZJAlVs z/Z7rLMk35B8xB2HmUKsag9W0Aw+M5vGgz8FfiCv4YpolcVa+0P32Coytmrb AvQdRmXjvS1rH6g9sFPoc+mqWNk06SiOSdnsVg6jKoANwGi/14jnL+dRgQpp cR/hMs0sOunwNwlRc/y0lMr55jNGK03KlPNUUpc7JfTp0aKNmmpmqBqpAj5k OYo6YlcztZWj09l9s+Ann2JUttVpSew/h3Mjd5gr/5XgqqL82ZuqEol0RuP2 ORE/qvXTmB2sEZnvYsaFMQgv569NBeVl08NcZO3Y7N2VshM23TrUXWFDW7Fl ftJChj2UlHu4cwIDAQABo4IDLDCCAygwDgYDVR0PAQH/BAQDAgXgMB0GA1Ud JQQWMBQGCCsGAQUFBwMEBggrBgEFBQcDAjAJBgNVHRMEAjAAMB8GA1UdIwQY MBaAFJPh/34d5fXkTeE5YoshaZXmr3IWMB0GA1UdDgQWBBT/FwVsblqS4AVr NlWlHYMUIzA0sDB/BggrBgEFBQcBAQRzMHEwbwYIKwYBBQUHMAKGY2h0dHA6 Ly9zeW5ndWxhcmlkLmNvbS5ici9yZXBvc2l0b3Jpby9hYy1zeW5ndWxhcmlk LW11bHRpcGxhL2NlcnRpZmljYWRvcy9hYy1zeW5ndWxhcmlkLW11bHRpcGxh LnA3YjCBggYDVR0gBHsweTB3BgdgTAECAYEFMGwwagYIKwYBBQUHAgEWXmh0 dHA6Ly9zeW5ndWxhcmlkLmNvbS5ici9yZXBvc2l0b3Jpby9hYy1zeW5ndWxh cmlkLW11bHRpcGxhL2RwYy9kcGMtYWMtc3luZ3VsYXJJRC1tdWx0aXBsYS5w ZGYwgcAGA1UdEQSBuDCBtaAgBgVgTAEDAqAXBBVBTlRPTklPIExVSVogQk9O T01JTkmgGQYFYEwBAwOgEAQOODM3Mzk5NDYwMDAxMDCgQgYFYEwBAwSgOQQ3 MDIwMjE5NjQ0ODI3MTQxMTkzNDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAw MDAwMDAwMDAwMKAXBgVgTAEDB6AOBAwwMDAwMDAwMDAwMDCBGWRpcmVjYW9A dHJhbnNsZW9uZS5jb20uYnIwgeIGA1UdHwSB2jCB1zBvoG2ga4ZpaHR0cDov L2ljcC1icmFzaWwuc3luZ3VsYXJpZC5jb20uYnIvcmVwb3NpdG9yaW8vYWMt c3luZ3VsYXJpZC1tdWx0aXBsYS9sY3IvbGNyLWFjLXN5bmd1bGFyaWQtbXVs dGlwbGEuY3JsMGSgYqBghl5odHRwOi8vc3luZ3VsYXJpZC5jb20uYnIvcmVw b3NpdG9yaW8vYWMtc3luZ3VsYXJpZC1tdWx0aXBsYS9sY3IvbGNyLWFjLXN5 bmd1bGFyaWQtbXVsdGlwbGEuY3JsMA0GCSqGSIb3DQEBCwUAA4ICAQBztRRD kJEHowz8IIla4jLOA5F4TSiEXEyN8qw24qOxSJdH8TVFhUHjgozSoaet49Y+ iSv6OlIBNtnPVqT3vZbMR9aCqN/3FXliiM6W3ytDxQCTNo7GJmFCrP1LazwT U6ivmDyWLBMLmcNHqzwJk6RwGUffWFsQ/zr0a2eaNf7WMYLo9A9ebnlt9kLt 2A/RpIDm9HBQ6EQYd3wdaofqPu9n07xedCog6M3lhMs0MVPqjaYgddgNPMjk EgMOs2R+7rIZdqql20pZ24PKTVSt0YfTkeQ0zTROefn2e24g+F87uFidQgV5 dBSESwt7q59kOyFuIgDHN8s2ZKvpXSJAPZ1l0krIdLP071FnDLyNshAw/8fU eGoKr1SnL6Tk1lLRR/ubrrqGLfaPxNP5hfgwog3ASwcC78YrNOvoVHyunevC RbAfIyBltEZlrcd2HtYhJ284ooCdo2bPTAUsigtP3HpUv3HdbKY8VlFNrxF+ boErZDAM0ctopDXTNbU5riQCopuX+CwpW2Djd+sk7Je0Q9YFquhu9BNZDx56 LSI+zuDX7mS31iWOWZab10a11wFmoSP2oXOeyew8G6vHrZDLECYhByhGgvjE C3uq66vrHoX/+D+RRezlYCKTSQ0QoHDf42kgv2RY+ekGGrjljMn1dOyr4Em1 Bn5H7NnYzzbVSa6ZczGBkDAjBgkqhkiG9w0BCRUxFgQUSggcVJ+SJwKt+Nhg nITQxmr+hUEwaQYJKoZIhvcNAQkUMVweWgBUAFIAQQBOAFMAUABPAFIAVABB AEQATwBSAEEAIABUAFIAQQBOAFMATABFAE8ATgBFACAATABUAEQAQQA6ADgA MwA3ADMAOQA5ADQANgAwADAAMAAxADAAMDCCB38GCyqGSIb3DQEMCgEDoIIH bjCCB2oGCiqGSIb3DQEJFgGgggdaBIIHVjCCB1IwggU6oAMCAQICCnBsK0Yl 2vYuuEEwDQYJKoZIhvcNAQENBQAwcDELMAkGA1UEBgwCQlIxEzARBgNVBAoM CklDUC1CcmFzaWwxNDAyBgNVBAsMK0F1dG9yaWRhZGUgQ2VydGlmaWNhZG9y YSBSYWl6IEJyYXNpbGVpcmEgdjUxFjAUBgNVBAMMDUFDIFN5bmd1bGFySUQw HhcNMjIwNDE4MTgzNTE0WhcNMjkwMzAxMjM1OTU5WjBbMQswCQYDVQQGEwJC UjEWMBQGA1UECwwNQUMgU3luZ3VsYXJJRDETMBEGA1UECgwKSUNQLUJyYXNp bDEfMB0GA1UEAwwWQUMgU3luZ3VsYXJJRCBNdWx0aXBsYTCCAiIwDQYJKoZI hvcNAQEBBQADggIPADCCAgoCggIBAJ34XqjqlDt8IkALicZwG/hdDgFcQTDf k2RMMlb0mwp4uC5+VzYbP38+43wd5hg7NSKbYlzXCYkQ4Dsu7c7pMD1UYC2y pVcudpKv7a/N3G7JCXEuhX7Duk9tsXzhDvLmdbifo0z2DsgRY4zlwcfVweDW zXKQhPCi/flkraZi0u+/yFKm5FLA96APox8h5px6jWsk5ip6+M/zsYk8TTWs hLCtsojuvOr5J6fuXcuht2scJZkJG7Fyp1XsijeCLilAUwyq8Upg7h4nr6H4 0/2MCfw4k9fZVDlqEBioVGse1p1gsOrnz3D59TbLJGSJQj8CqGPEUeMwrizv Kc2/4QjaFAH9EzY50vYBz9YVRNDw6j8QfTk+3ozLZjwFiyMOuP9pvXMvJVfF FztW19bU6DBfqtKQPy08A8rXcNQKDPznDalWUS8WufUWbLi2yzywtLc0Xtdw s1wYHnG6LZijYtoQGljBMn45r3CRVSPQlKpZjXp8VCH/Ybsu56zQETiwuOwJ q05rhSL5QIUQW+5ruBq2H7FIPyLioQlx6QaU9NWgXRRwM9AWIHSVXej788g/ cJBPTLX2/NQBavXloDp7egsg9Q2ZeXAr+naVv1C1zPjmxMV+Ib4Kx/MmR2WT rCCqYpGKHSC2UoEZtavSXn2LGvJ0ZYP1hoCLIDHr17oc8C3/+V1PAgMBAAGj ggIBMIIB/TAOBgNVHQ8BAf8EBAMCAQYwDwYDVR0TAQH/BAUwAwEB/zAfBgNV HSMEGDAWgBRQOH1C5FHJB0NYY8BrY6z/oHPz4DAdBgNVHQ4EFgQUk+H/fh3l 9eRN4TliiyFpleavchYwgdgGA1UdIASB0DCBzTBlBgdgTAECAYEFMFowWAYI KwYBBQUHAgEWTGh0dHA6Ly9zeW5ndWxhcmlkLmNvbS5ici9yZXBvc2l0b3Jp by9hYy1zeW5ndWxhcmlkL2RwYy9kcGMtYWMtc3luZ3VsYXJJRC5wZGYwZAYG YEwBAgN9MFowWAYIKwYBBQUHAgEWTGh0dHA6Ly9zeW5ndWxhcmlkLmNvbS5i ci9yZXBvc2l0b3Jpby9hYy1zeW5ndWxhcmlkL2RwYy9kcGMtYWMtc3luZ3Vs YXJJRC5wZGYwgb4GA1UdHwSBtjCBszBSoFCgToZMaHR0cDovL3N5bmd1bGFy aWQuY29tLmJyL3JlcG9zaXRvcmlvL2FjLXN5bmd1bGFyaWQvbGNyL2xjci1h Yy1zeW5ndWxhcmlkLmNybDBdoFugWYZXaHR0cDovL2ljcC1icmFzaWwuc3lu Z3VsYXJpZC5jb20uYnIvcmVwb3NpdG9yaW8vYWMtc3luZ3VsYXJpZC9sY3Iv bGNyLWFjLXN5bmd1bGFyaWQuY3JsMA0GCSqGSIb3DQEBDQUAA4ICAQCW7E3O AdtDdKiY+mwkmLDctRNFjfQapqrPLqwwcu4n+izZ8UWzzQ5BWWxOUiPycQOP 12MdrTM43cXSGRhk0yRaD9FdsjWTV0/bOQG/5LBRV3sfHkmp3XdRK2l86FBw 5q1XRfy01EqjWXc72bz4JQh5E16MTCScnDL2sjk+mrFcvVFqgolSb2RQPKDl 7y0sEfA7ejNjXASvTaQ2mqoIximZR3itoQzDYEVGlVivULllMJSVDKivFPEI udYjiZDPY6b9RYOOtE2ah1OwcC1NWKCOwA2trKFR2DXGWmlijui/u+Heo907 nkPI3Jx8PJqF1eM2ErDEGrvsgOSJUZQ2F0JeIgnBtsAWxADDbiVApoINhvo4 oQEH11kfyUQwgWjT6xalST5GahiqjetIAmr+nMW2Ch9mUoqWPZzu1t7ErSYy KeXVJOeS1QHRjD3PSGA4mDEee0ymZvkF/80+m5YgOC69BJ2Hjj/0L5dokUqy +nUgIMqsaWWtoR++cMPs1rDuagG5wmMCJE74nYsVzmqeVQSWABtXfqPiEPfo UHbSgquOq0AnjldC/O5TQnQu4mnS0cZWH3j6IX2GNGr0doptZcZUzzy1FX2D xd7RmBBHEKFOiUnQpyPUgc9FDhzYClHPqyvVdN+HgqokglFGPpfIn2hAHbTG Z2KceZPkywgMB4euvDCCBs4GCyqGSIb3DQEMCgEDoIIGvTCCBrkGCiqGSIb3 DQEJFgGgggapBIIGpTCCBqEwggSJoAMCAQICAQEwDQYJKoZIhvcNAQENBQAw gZcxCzAJBgNVBAYTAkJSMRMwEQYDVQQKDApJQ1AtQnJhc2lsMT0wOwYDVQQL DDRJbnN0aXR1dG8gTmFjaW9uYWwgZGUgVGVjbm9sb2dpYSBkYSBJbmZvcm1h Y2FvIC0gSVRJMTQwMgYDVQQDDCtBdXRvcmlkYWRlIENlcnRpZmljYWRvcmEg UmFpeiBCcmFzaWxlaXJhIHY1MB4XDTE2MDMwMjEzMDEzOFoXDTI5MDMwMjIz NTkzOFowgZcxCzAJBgNVBAYTAkJSMRMwEQYDVQQKDApJQ1AtQnJhc2lsMT0w OwYDVQQLDDRJbnN0aXR1dG8gTmFjaW9uYWwgZGUgVGVjbm9sb2dpYSBkYSBJ bmZvcm1hY2FvIC0gSVRJMTQwMgYDVQQDDCtBdXRvcmlkYWRlIENlcnRpZmlj YWRvcmEgUmFpeiBCcmFzaWxlaXJhIHY1MIICIjANBgkqhkiG9w0BAQEFAAOC Ag8AMIICCgKCAgEA9y14Gm1FrBfoFF8P+mDg3hdl5KhMn5N1XoXbCMft/xEY HYwkv6LsWEdBKYtoXaDFFSQzX910H4wFlycgqE53h2AJdjHDHb6KrSwdopXk JSYorNH07WEyWsnnvQQCHSKwqK9S3NL6M1wqQ7auZ08IYyKHGmbXLhbHa5Jf p232MZeZu7405EzEuo/H345hc2zTw5Sbld1cuwM2qT1SazJelBz1whzGDM5d CsUf+O0uVxoJi2JGLfGzQhwN34NO8vhrb4LvkjYTD8FiiIMIWMPvbxKzp2r0 YTopmKODNgMDaTStDiiyOyJuVdPeIxaeE8vppZn0B9NJPJ5ElBDftUxhpIb9 enNobpG8fYgn25pO4ptoRio0APHh+uFT2KEtqnSmz23d8QwxzdnK6GCqctlN qEE1QUn/YQt6OnyqN86dFN3TYsO2YFikvVMD99UNIMIAdaK2YC7hfiyKaR2M MXExvwKLzVmuLEz7pDIl9i/qe7iBxMU2SSVOGKA2nEIf5n2zyM99utR3uGBZ kN+bH8N3ih0Eji8olX3r/59Jf699PD4oxHfnfh8+UZqiM0vI2F2SHiccpuJh IxNXyqXj+ehGudi5SCfGarCd8cx1rt1pAFc+ITKq1+gTzBltiMTrhFEMiGgg YNZSOkC4jzB76Nvye4LuGH6kXB+otu1tx8s2phECAwEAAaOB9TCB8jBOBgNV HSAERzBFMEMGBWBMAQEAMDowOAYIKwYBBQUHAgEWLGh0dHA6Ly9hY3JhaXou aWNwYnJhc2lsLmdvdi5ici9EUENhY3JhaXoucGRmMD8GA1UdHwQ4MDYwNKAy oDCGLmh0dHA6Ly9hY3JhaXouaWNwYnJhc2lsLmdvdi5ici9MQ1JhY3JhaXp2 NS5jcmwwHwYDVR0jBBgwFoAUaai+ddnE72znE0XkYW7laPi2QF4wHQYDVR0O BBYEFGmovnXZxO9s5xNF5GFu5Wj4tkBeMA8GA1UdEwEB/wQFMAMBAf8wDgYD VR0PAQH/BAQDAgEGMA0GCSqGSIb3DQEBDQUAA4ICAQAUbdvyYlmqXn+6P6ZY UeD8cppTCKfyXmeRdAWdV2n5mKZeYOqUaJBayETBVy8hIZYKOdSNOSJ8gnPU 3elF45bDgEk3Py0Wr8owf/DXEI5UWA4esX+znT9OZhXE/9Pum2EbTYLJ1G69 dKX7Cv1M8lL0Fm7phkue4QmawGTsnfTM7pIhf2swGuiX6GZNpw5BLnSjmPLU ck6TJg8zeX7nz0gt7+7UiVKk6qOc+66DfxZnJEcxgGMs+V7pWznOf1LAE/7Z Pij/3JW26nf4YXr5pnSXJS6/almWXDoM7hVNmLID66LVbtHxTVxq+iivX7uO KTh2o5jLE1yt1+6/z8IdfZBUaMORd0UDFGQukXtGC314fsqZ1EgswPri+CT7 RyBsr5G1orpZLrLaTilElcAT87wNrEFDr6nWwEewvI3B2BSTLith2RY4u6cX Flmm/zSrBXCd7Jbg45oYs6UGsW2E3wFV7t6vMndLLvwRx4QabXsh72Mss9BG /S1MrEdnDLVr3z1Or56D4GLzhjtQs4shhLIClC5Ah+KNlYJdYELiljuFevKx VvGv476tvLG2PsvwKNCOaIaZwXEDgGsIhW8+sibTm2o2DLs7Qwz/KGOuehLr iVwSYbigCTyP+1g7YH1fH3zof4kBkAE1TLYAzdIL/G5+TXVF/FDDV9bB4VNx 9VKaFjCCBnUGCyqGSIb3DQEMCgEDoIIGZDCCBmAGCiqGSIb3DQEJFgGgggZQ BIIGTDCCBkgwggQwoAMCAQICCQDrL0Xy42Le0DANBgkqhkiG9w0BAQ0FADCB lzELMAkGA1UEBhMCQlIxEzARBgNVBAoMCklDUC1CcmFzaWwxPTA7BgNVBAsM NEluc3RpdHV0byBOYWNpb25hbCBkZSBUZWNub2xvZ2lhIGRhIEluZm9ybWFj YW8gLSBJVEkxNDAyBgNVBAMMK0F1dG9yaWRhZGUgQ2VydGlmaWNhZG9yYSBS YWl6IEJyYXNpbGVpcmEgdjUwHhcNMjIwMzIxMTgwMDIxWhcNMjkwMzAyMTIw MDIxWjBwMQswCQYDVQQGDAJCUjETMBEGA1UECgwKSUNQLUJyYXNpbDE0MDIG A1UECwwrQXV0b3JpZGFkZSBDZXJ0aWZpY2Fkb3JhIFJhaXogQnJhc2lsZWly YSB2NTEWMBQGA1UEAwwNQUMgU3luZ3VsYXJJRDCCAiIwDQYJKoZIhvcNAQEB BQADggIPADCCAgoCggIBAKfnYtHF9VAnJvG6e8LdTc+6rh5jO7Rjx/a4uv+g piD94ct1sOIrxcTiUmLFMKMSXLOEogN6Dq+yAK+lUZGKNXqIXBPmUDhJxOyH eQA9fXQXjpnIGH6x+5Dikq5hXmiV7PiggF4HdOFs6lUSjDLKBCCKbPUh1MMC +CkkdxHMsnd5jJSTwbcqGj6gc/nvJ2N4vyVRb1SKE8aASiaiclLb0tJdG2om VDwbRt83eXrOuTjOIdrWJK1gx71qH3x2kHSCQtpHuEiKI19KM01mnjHdAM+F Egin2/zIRYNlrcGpVimTgShohvH6bChK6vYMaBqaC5qpL6twJR5Om6ZIX5xL Py6XVdE323grP8Hiut1KDF6kjjJCK/HjvZi42aF4OcQ4heU1tRB9VxWNo/dT g4GfsZTn+ijvv9jQNupyXx23icJtHkYOa4D2RQlHZPS3/a1FxA6onWcEJqH8 WujhjfZa9EnneB6UHoHmnMn2zqwQfDH3DjKlH/aGb4pg5alxEFGkZIDdgyeR KpsN9gBNQD3akP57dG6yOj1beKBHXFdOsG+19mlknNgt1efNiak79IOiNvdN YXrL32nVWziwNdfLjaHYrtpcY4v3uJkml3BBDuLt9ITbVP+s1wDTodCbX+V/ JR3M2jRao9gU/QMXSY5RVgWdr+0sBHAkyi0hQSwcUzflAgMBAAGjgbwwgbkw DgYDVR0PAQH/BAQDAgEGMBUGA1UdIAQOMAwwCgYGYEwBAYEcMAAwPwYDVR0f BDgwNjA0oDKgMIYuaHR0cDovL2FjcmFpei5pY3BicmFzaWwuZ292LmJyL0xD UmFjcmFpenY1LmNybDAfBgNVHSMEGDAWgBRpqL512cTvbOcTReRhbuVo+LZA XjAdBgNVHQ4EFgQUUDh9QuRRyQdDWGPAa2Os/6Bz8+AwDwYDVR0TAQH/BAUw AwEB/zANBgkqhkiG9w0BAQ0FAAOCAgEAfWRoqW/irKhqGnhOafAXhhZ24/FF 3xKFwT+StYII+jFLs499x72ar6ydOrlTqSBZVF+tADcWLEnsTA6ks4F2HG9t WHMW0XA9X2HauZapO+fYvQXhiukr38jXimhMKXYQUCtuL5E9Z8T+zHsjfLy8 JXXITZTNvBq0blMMB0zqITfe947j8AHZyVCAMT472c83zbNjquP5LEGxYDm2 +pbQUQ70ttCeMAVGw0o5JEsMNoED9PDRbUFwf9b6RcD+sHX8KZiEObR6YtFA VtaySWfQKhoUt3IgdaJtXIRvYkWv0Mwaghxxjf9IZa3bGTMRCnaLLrzT6YWi cJTUvn36O/Slj6eii3q8dTaukCFS08qr1iKpqUY4xU7+3ccKBa/IqKvvNSv5 0aLTLuQ9Anqqs1F5WwlbSwziDFEjlUOtSAR09TcKFXgATpdqhHSfrAMQ7sRc yMofRVYQROL5x2gwK29vMhEgb4VEJqBBq00qeBHvMp7t1ZFyPYcVbEBqKGW3 9AqROUSlo9ky1tHb8SMhnNch+SyzMu+XoyMqLJDeq0pDvJgsumwTqrVuUaVO 3mRvtue4WfMuWS+u1Vl/nh8jLna1MNdIARuHcv7HYNq/NkAC9Yrqj3d+2R8u BjSrq7WFD6LuiGdfMTz7PyloJSmvtNDxK3RpS4DSGL/GOkAWm3/Rui0wggWt BgkqhkiG9w0BBwGgggWeBIIFmjCCBZYwggWSBgsqhkiG9w0BDAoBAqCCBO4w ggTqMBwGCiqGSIb3DQEMAQMwDgQIC0wwZsRTqg4CAggABIIEyAQw9fUYi/Fv Lz96t91u62YfJ/aQ6/bdOP8BfeV3cZuWmfp6o6o8bfqj+wdgSdJa/tuZs/Vs jDp0Pt2+uDF5ym9zkIdRaaERPD5T/Qk5Q9MJG+lTTXp7X9QIHYbugVD5eUNN IPSsZeO8qCV/xhY66iW+KPO5qL28Z0z+sZka9ElB/kWKYPyiziW02QNouits Vp8xxN/gAauZmHKWieRhvhJjT2b7TRYd0MKe4ThbBJpj8fLcPMQ8T28okqkx qbGz787BM0kZJQDpHPQoV2791166wASMRKZ1mMSTkQXy4lJzmG0xvtFSFsh+ lo5te9JUnu3tBa1LelqX43uUmbh9oa5em2ejymnvWkuncD8LOAXMzd8p4qM6 LK4TTcUpOSsyMSRtg7Ko35BHm9iJ4UGZkOBBzA1ZDGjCj2BWfrtUIRIPuDJ/ kkHcf0Gif3/K5FG2qUeQ7tzx7zb5xmuQnV5/dpy7T+xNMQiyBgvqOjck1e9K 4SpGpnpXaGqDLIoC854ex0I3qn1TEzTQc7WRWX192nI59ZGjLSMirDSGcwP7 Q9V0qbuUcAqv1Oru7rZQlqAP0+DuKY1D0Nnci20KgbNf1i0kEHjtBEVtUOA+ +VJNfu1WXcEZbjtaN8OEgJdlGSIOKyFQ8XJvjh+OdlCXmVnzBDI0BxEoZHPs OGXdEmzZ9e3XhQEMBnZKKLf06UvDds2gfCcZZxbV7wrUKIoaGFjss5qL2PYE oPy2ecEWXyUJKx4dazFR5H9FUjdQFQTUnxuy7EXph88XBeyF3vVLXsQD4rx3 X2jGp9Hi00cjnF2gfUrZzJhiUG/1RyUDGGO8scGeYDCAu0iZg/UCIE1yipSJ 2NGY8xznLA/hitZQ/xppEBtLTmsmWe7VAuK2rw/ItL8eOk+YuJK3/u+aMKpw 9gKAJukpwZg5dhKJgVhOo6LgNo07PsavTrsA2lRbvwl1Robk13O4fQ0MKZLK 0UCn13JYmkvU/TsIuegpqNOkbhZv6uy8ulrvMXouQ+G/rjvsOQ7k/cnPoBhP qDo+jOYsVEniP8MbPPAHRSJqfxcpEyyvrz2FcPaWM7QBuaLoCtD8XS7OVVd+ rEJv9Y/rOL3Xx3i4S96oNLaZO2TqXLldoGlzAl9JgvLqxOESDeK9AQiBbSDO AQ7zy/vWSOevXUSbqINN468qaZYde4vx23uoh7gtof9eaDKCzBYXjg+7kcic gPcDK2ZoGrDaZ5UTjLIfCfwUZoZNatpuEGAgjbkfy8fdS6khF2A+VmXkrayx WzcG/tKgR2XIAp26nXHsuLrZSbw94uV4VWeN4xwYjNIHYzbm5rS2x4BBbtVw hN5SqTl7dTeWK7VZc+KbkhamtdoeU5F13mXCcmB+lQJ+mqMZiSRx/7pE2cL7 bosBWNsjmly8zDcwRnJnYoT/59qUPwn8Hrxf2QRfLckm3J7c6lJzEAOEPMTe 1zVj7vqC2QjNAlxo5zpyMLXRoQPFn1M8a3dWWurVAhXdkDLnT+r+z/N8eWND 7xRBq8tERj2KW8m0opiLGxA6u6BQue5O9Gq+AmhVYKfjNx8lxCwLyt0+dWko s/P0q0dAJrov0wpIBW3uNDYU/7CNhX+3iF1G4FtiPtffqJDm0nJZuZHRNQXy STGBkDAjBgkqhkiG9w0BCRUxFgQUSggcVJ+SJwKt+NhgnITQxmr+hUEwaQYJ KoZIhvcNAQkUMVweWgBUAFIAQQBOAFMAUABPAFIAVABBAEQATwBSAEEAIABU AFIAQQBOAFMATABFAE8ATgBFACAATABUAEQAQQA6ADgAMwA3ADMAOQA5ADQA NgAwADAAMAAxADAAMDAxMCEwCQYFKw4DAhoFAAQUHT64R2vhyZ2iz04rS8Wg G6SR8TkECDJjnThbVNUFAgIIAA== """

# Remova espaços, quebras de linha etc.
clean_base64 = base64_string.replace('\n', '').replace('\r', '').replace(' ', '')

# Decodifica e escreve o arquivo .pfx
with open("certificado.pfx", "wb") as f:
    f.write(base64.b64decode(clean_base64))

print("Arquivo .pfx salvo como certificado.pfx")